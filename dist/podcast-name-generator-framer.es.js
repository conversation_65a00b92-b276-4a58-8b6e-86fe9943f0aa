import Se, { useState as b } from "react";
var pe = { exports: {} }, B = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ke;
function Te() {
  if (ke) return B;
  ke = 1;
  var N = Symbol.for("react.transitional.element"), $ = Symbol.for("react.fragment");
  function x(R, f, g) {
    var S = null;
    if (g !== void 0 && (S = "" + g), f.key !== void 0 && (S = "" + f.key), "key" in f) {
      g = {};
      for (var P in f)
        P !== "key" && (g[P] = f[P]);
    } else g = f;
    return f = g.ref, {
      $$typeof: N,
      type: R,
      key: S,
      ref: f !== void 0 ? f : null,
      props: g
    };
  }
  return B.Fragment = $, B.jsx = x, B.jsxs = x, B;
}
var Q = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ne;
function Ae() {
  return Ne || (Ne = 1, process.env.NODE_ENV !== "production" && function() {
    function N(e) {
      if (e == null) return null;
      if (typeof e == "function")
        return e.$$typeof === me ? null : e.displayName || e.name || null;
      if (typeof e == "string") return e;
      switch (e) {
        case T:
          return "Fragment";
        case te:
          return "Profiler";
        case ee:
          return "StrictMode";
        case G:
          return "Suspense";
        case de:
          return "SuspenseList";
        case z:
          return "Activity";
      }
      if (typeof e == "object")
        switch (typeof e.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), e.$$typeof) {
          case ie:
            return "Portal";
          case le:
            return (e.displayName || "Context") + ".Provider";
          case fe:
            return (e._context.displayName || "Context") + ".Consumer";
          case ce:
            var d = e.render;
            return e = e.displayName, e || (e = d.displayName || d.name || "", e = e !== "" ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
          case ne:
            return d = e.displayName || null, d !== null ? d : N(e.type) || "Memo";
          case H:
            d = e._payload, e = e._init;
            try {
              return N(e(d));
            } catch {
            }
        }
      return null;
    }
    function $(e) {
      return "" + e;
    }
    function x(e) {
      try {
        $(e);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var m = d.error, p = typeof Symbol == "function" && Symbol.toStringTag && e[Symbol.toStringTag] || e.constructor.name || "Object";
        return m.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          p
        ), $(e);
      }
    }
    function R(e) {
      if (e === T) return "<>";
      if (typeof e == "object" && e !== null && e.$$typeof === H)
        return "<...>";
      try {
        var d = N(e);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var e = A.A;
      return e === null ? null : e.getOwner();
    }
    function g() {
      return Error("react-stack-top-frame");
    }
    function S(e) {
      if (ae.call(e, "key")) {
        var d = Object.getOwnPropertyDescriptor(e, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return e.key !== void 0;
    }
    function P(e, d) {
      function m() {
        K || (K = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(e, "key", {
        get: m,
        configurable: !0
      });
    }
    function j() {
      var e = N(this.type);
      return ue[e] || (ue[e] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), e = this.props.ref, e !== void 0 ? e : null;
    }
    function Z(e, d, m, p, k, w, L, F) {
      return m = w.ref, e = {
        $$typeof: W,
        type: e,
        key: d,
        props: w,
        _owner: k
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(e, "ref", {
        enumerable: !1,
        get: j
      }) : Object.defineProperty(e, "ref", { enumerable: !1, value: null }), e._store = {}, Object.defineProperty(e._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(e, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(e, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: L
      }), Object.defineProperty(e, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: F
      }), Object.freeze && (Object.freeze(e.props), Object.freeze(e)), e;
    }
    function U(e, d, m, p, k, w, L, F) {
      var h = d.children;
      if (h !== void 0)
        if (p)
          if (re(h)) {
            for (p = 0; p < h.length; p++)
              I(h[p]);
            Object.freeze && Object.freeze(h);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else I(h);
      if (ae.call(d, "key")) {
        h = N(e);
        var C = Object.keys(d).filter(function(se) {
          return se !== "key";
        });
        p = 0 < C.length ? "{key: someKey, " + C.join(": ..., ") + ": ...}" : "{key: someKey}", V[h + p] || (C = 0 < C.length ? "{" + C.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          p,
          h,
          C,
          h
        ), V[h + p] = !0);
      }
      if (h = null, m !== void 0 && (x(m), h = "" + m), S(d) && (x(d.key), h = "" + d.key), "key" in d) {
        m = {};
        for (var X in d)
          X !== "key" && (m[X] = d[X]);
      } else m = d;
      return h && P(
        m,
        typeof e == "function" ? e.displayName || e.name || "Unknown" : e
      ), Z(
        e,
        h,
        w,
        k,
        f(),
        m,
        L,
        F
      );
    }
    function I(e) {
      typeof e == "object" && e !== null && e.$$typeof === W && e._store && (e._store.validated = 1);
    }
    var O = Se, W = Symbol.for("react.transitional.element"), ie = Symbol.for("react.portal"), T = Symbol.for("react.fragment"), ee = Symbol.for("react.strict_mode"), te = Symbol.for("react.profiler"), fe = Symbol.for("react.consumer"), le = Symbol.for("react.context"), ce = Symbol.for("react.forward_ref"), G = Symbol.for("react.suspense"), de = Symbol.for("react.suspense_list"), ne = Symbol.for("react.memo"), H = Symbol.for("react.lazy"), z = Symbol.for("react.activity"), me = Symbol.for("react.client.reference"), A = O.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ae = Object.prototype.hasOwnProperty, re = Array.isArray, Y = console.createTask ? console.createTask : function() {
      return null;
    };
    O = {
      "react-stack-bottom-frame": function(e) {
        return e();
      }
    };
    var K, ue = {}, J = O["react-stack-bottom-frame"].bind(
      O,
      g
    )(), v = Y(R(g)), V = {};
    Q.Fragment = T, Q.jsx = function(e, d, m, p, k) {
      var w = 1e4 > A.recentlyCreatedOwnerStacks++;
      return U(
        e,
        d,
        m,
        !1,
        p,
        k,
        w ? Error("react-stack-top-frame") : J,
        w ? Y(R(e)) : v
      );
    }, Q.jsxs = function(e, d, m, p, k) {
      var w = 1e4 > A.recentlyCreatedOwnerStacks++;
      return U(
        e,
        d,
        m,
        !0,
        p,
        k,
        w ? Error("react-stack-top-frame") : J,
        w ? Y(R(e)) : v
      );
    };
  }()), Q;
}
process.env.NODE_ENV === "production" ? pe.exports = Te() : pe.exports = Ae();
var s = pe.exports;
function Pe({
  className: N = "",
  style: $ = {}
}) {
  const [x, R] = b(""), [f, g] = b([]), [S, P] = b([]), [j, Z] = b(!1), [U, I] = b(null), [O, W] = b(null), [ie, T] = b([]), [ee, te] = b({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [fe, le] = b(!1), [ce, G] = b(!1), [de, ne] = b(!1), [H, z] = b(/* @__PURE__ */ new Set()), [me, A] = b(/* @__PURE__ */ new Set()), [ae, re] = b(/* @__PURE__ */ new Set()), [Y, K] = b(null), [ue, J] = b(0), [v, V] = b(!1), e = 100, d = (t) => {
    const n = window.scrollY, r = document.documentElement.scrollHeight, i = window.innerHeight, l = r - n - i;
    t(), requestAnimationFrame(() => {
      const o = document.documentElement.scrollHeight, a = window.innerHeight, c = o - l - a;
      Math.abs(o - r) > 5 ? window.scrollTo(0, Math.max(0, c)) : window.scrollTo(0, n);
    });
  }, m = () => {
    const t = document.createElement("canvas"), n = t.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const r = t.toDataURL(), i = navigator.userAgent, l = navigator.language, o = Intl.DateTimeFormat().resolvedOptions().timeZone, a = r + i + l + o;
    let c = 0;
    for (let u = 0; u < a.length; u++) {
      const E = a.charCodeAt(u);
      c = (c << 5) - c + E, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, p = () => {
    const t = m(), n = (/* @__PURE__ */ new Date()).toDateString(), r = `${t}_${n}`, i = localStorage.getItem(r), l = i ? parseInt(i, 10) : 0;
    return J(l), l >= e ? (V(!0), !1) : !0;
  }, k = (t = 1) => {
    const n = m(), r = (/* @__PURE__ */ new Date()).toDateString(), i = `${n}_${r}`, l = localStorage.getItem(i), a = (l ? parseInt(l, 10) : 0) + t;
    localStorage.setItem(i, a.toString()), J(a), a >= e && V(!0);
  };
  Se.useEffect(() => {
    p();
  }, []);
  const w = (t) => {
    let n = t.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const r = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], i = n.split(" "), l = i.filter(
      (a) => a.length > 2 && !r.includes(a)
    );
    if (l.length === 1) {
      const a = l[0];
      return a.length >= 6 && a.length <= 15 ? a : a.length < 6 ? a + "pod" : L(a);
    }
    if (l.length >= 2) {
      const a = l[0], c = l[1], u = a + c;
      if (u.length >= 6 && u.length <= 15)
        return u;
    }
    const o = i[0];
    return o && o.length >= 3 ? L(o) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, L = (t) => {
    if (t.length <= 8) return t;
    const n = {
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      technology: "tech",
      development: "dev",
      stories: "story",
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal"
    };
    return n[t] ? n[t] : t.length > 8 ? t.substring(0, 8) : t;
  }, F = async (t) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${t}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const r = await n.json();
      return r.Answer && r.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, h = async (t) => {
    const n = [...t];
    for (let r = 0; r < n.length; r++) {
      const i = w(n[r].name);
      n[r].suggestedDomain = i, n[r].domainStatus = "checking", z((l) => /* @__PURE__ */ new Set([...l, r]));
    }
    g(n);
    for (let r = 0; r < n.length; r++) {
      const i = `${n[r].suggestedDomain}.com`;
      try {
        const l = await F(i);
        g((o) => {
          const a = [...o];
          return a[r] && (a[r].domainStatus = l), a;
        });
      } catch {
        g((o) => {
          const a = [...o];
          return a[r] && (a[r].domainStatus = "error"), a;
        });
      } finally {
        z((l) => {
          const o = new Set(l);
          return o.delete(r), o;
        });
      }
    }
  }, C = (t, n) => {
    if (t.length === 0) return null;
    const r = t.map((a) => a.name.split(" ").length), i = n.map((a) => a.name.split(" ").length), l = r.reduce((a, c) => a + c, 0) / r.length, o = i.length > 0 ? i.reduce((a, c) => a + c, 0) / i.length : 0;
    return l <= 2 && o > 2 ? "short" : l <= 4 && (o <= 2 || o > 4) ? "medium" : l > 4 && o <= 4 ? "long" : l <= 2 ? "short" : l <= 4 ? "medium" : "long";
  }, X = (t, n) => {
    if (t.length === 0) return null;
    const r = t.map((l) => l.description.toLowerCase()).join(" "), i = n.map((l) => l.description.toLowerCase()).join(" ");
    return (r.includes("professional") || r.includes("business")) && !i.includes("professional") && !i.includes("business") ? "professional" : (r.includes("creative") || r.includes("unique")) && !i.includes("creative") && !i.includes("unique") ? "creative" : (r.includes("fun") || r.includes("playful")) && !i.includes("fun") && !i.includes("playful") ? "playful" : "descriptive";
  }, se = (t) => {
    const n = [];
    t.forEach((i) => {
      const l = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], o = i.name.toLowerCase().split(/\s+/).filter(
        (a) => a.length > 2 && !l.includes(a)
      );
      n.push(...o);
    });
    const r = {};
    return n.forEach((i) => r[i] = (r[i] || 0) + 1), Object.entries(r).sort(([, i], [, l]) => l - i).slice(0, 5).map(([i]) => i);
  }, ge = (t) => {
    const n = t.filter((i) => i.liked === !0), r = t.filter((i) => i.liked === !1);
    return {
      preferredLength: C(n, r),
      preferredStyle: X(n, r),
      likedKeywords: se(n),
      dislikedKeywords: se(r),
      preferredStructure: null
    };
  }, je = (t, n) => {
    const r = [
      ...n.likedNames.map((a) => a.name.toLowerCase()),
      ...n.dislikedNames.map((a) => a.name.toLowerCase()),
      ...f.map((a) => a.name.toLowerCase())
    ], i = `Create 4 unique, high-converting podcast names for: ${t}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    r.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${r.map((a) => `- ${a}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let o = "";
    if (n.patterns.preferredLength && (o += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (o += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (o += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (o += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const a = n.likedNames.slice(-2).map((c) => c.name).join('", "');
      o += `
Generate names with similar appeal to: "${a}" (but completely different concepts). `;
    }
    return `${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, Ee = (t, n, r = 1) => {
    const i = [
      ...n.likedNames.map((c) => c.name.toLowerCase()),
      ...n.dislikedNames.map((c) => c.name.toLowerCase()),
      ...f.map((c) => c.name.toLowerCase())
    ], l = `Create ${r} unique, high-converting podcast name${r > 1 ? "s" : ""} for: ${t}`;
    let o = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    i.length > 0 && (o += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let a = "";
    return n.patterns.likedKeywords.length > 0 && (a += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (a += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${l}${o}${a}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${r > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, he = async (t = !1) => {
    if (!x.trim()) {
      I("Please describe what your podcast is about");
      return;
    }
    if (!p()) {
      I(null);
      return;
    }
    Z(!0), I(null), g([]), t ? G(!0) : (T([]), le(!1), G(!1), ne(!1));
    try {
      const n = t ? je(x, ee) : `Create 4 unique, high-converting podcast names for: ${x}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, r = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!r.ok)
        throw new Error(`API request failed: ${r.status} ${r.statusText}`);
      const i = await r.json();
      if (!i.candidates || !i.candidates[0] || !i.candidates[0].content)
        throw new Error("Invalid response format from API");
      const o = i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!o)
        throw new Error("No valid JSON found in API response");
      const a = JSON.parse(o[0]);
      if (!a.podcast_names || !Array.isArray(a.podcast_names))
        throw new Error("Invalid response structure");
      g(a.podcast_names), k(4), h(a.podcast_names);
      const c = a.podcast_names.map((u, E) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: E
      }));
      T(c);
    } catch (n) {
      console.error("Error generating podcast names:", n), I(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      Z(!1), G(!1);
    }
  }, be = async (t, n) => {
    try {
      await navigator.clipboard.writeText(t), W(n), setTimeout(() => W(null), 2e3);
    } catch (r) {
      console.error("Failed to copy text:", r);
    }
  }, xe = async (t, n) => {
    const r = f[t];
    if (r) {
      if (n) {
        const i = window.scrollY, l = document.documentElement.scrollHeight;
        re((o) => /* @__PURE__ */ new Set([...o, t])), d(() => {
          K(`"${r.name}" added to favorites!`);
        }), setTimeout(() => {
          d(() => {
            K(null);
          });
        }, 2e3), setTimeout(() => {
          const o = document.querySelector(".favorites-section"), a = o ? o.scrollHeight : 0;
          P((c) => c.find((u) => u.name === r.name) ? c : [...c, r]), requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              const u = (o ? o.scrollHeight : 0) - a, M = document.documentElement.scrollHeight - l, oe = u > 0 ? u : M, ve = i + oe;
              window.scrollTo(0, Math.max(0, ve));
            });
          });
        }, 100), setTimeout(() => {
          re((o) => {
            const a = new Set(o);
            return a.delete(t), a;
          });
        }, 700), te((o) => {
          const a = { ...o };
          return a.dislikedNames = a.dislikedNames.filter((c) => c.name !== r.name), a.likedNames.find((c) => c.name === r.name) || a.likedNames.push({
            name: r.name,
            description: r.description,
            liked: !0,
            timestamp: Date.now(),
            index: t
          }), a.patterns = ge([...a.likedNames, ...a.dislikedNames]), a;
        }), A((o) => /* @__PURE__ */ new Set([...o, t]));
      } else {
        const i = window.scrollY;
        A((l) => /* @__PURE__ */ new Set([...l, t])), te((l) => {
          const o = { ...l };
          return o.likedNames = o.likedNames.filter((a) => a.name !== r.name), o.dislikedNames.find((a) => a.name === r.name) || o.dislikedNames.push({
            name: r.name,
            description: r.description,
            liked: !1,
            timestamp: Date.now(),
            index: t
          }), o.patterns = ge([...o.likedNames, ...o.dislikedNames]), o;
        }), setTimeout(() => {
          window.scrollTo(0, i);
        }, 0);
      }
      x.trim() && Re(t), de || ne(!0);
    }
  }, Re = async (t) => {
    var n, r, i, l, o, a;
    if (p())
      try {
        const c = Ee(x, ee, 1), u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (!u.ok)
          throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);
        const M = (o = (l = (i = (r = (n = (await u.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : r.content) == null ? void 0 : i.parts) == null ? void 0 : l[0]) == null ? void 0 : o.text;
        if (!M)
          throw new Error("No content in API response");
        const oe = M.match(/\{[\s\S]*\}/);
        if (!oe)
          throw new Error("No valid JSON found in response");
        const q = (a = JSON.parse(oe[0]).podcast_names) == null ? void 0 : a[0];
        if (q) {
          if (g((D) => {
            const y = [...D];
            return y[t] = {
              name: q.name,
              description: q.description,
              suggestedDomain: q.suggestedDomain,
              domainStatus: "checking"
            }, y;
          }), A((D) => {
            const y = new Set(D);
            return y.delete(t), y;
          }), T((D) => D.filter((_) => _.index !== t)), q.suggestedDomain) {
            z((y) => /* @__PURE__ */ new Set([...y, t]));
            const D = await F(q.suggestedDomain);
            g((y) => {
              const _ = [...y];
              return _[t] && (_[t].domainStatus = D), _;
            }), z((y) => {
              const _ = new Set(y);
              return _.delete(t), _;
            });
          }
        } else
          throw new Error("No new name found in API response");
      } catch (c) {
        console.error(`Error generating replacement suggestion for index ${t}:`, c), A((u) => {
          const E = new Set(u);
          return E.delete(t), E;
        }), T((u) => u.filter((M) => M.index !== t));
      }
  }, we = (t) => {
    t.preventDefault(), he();
  }, ye = (t) => {
    t.key === "Enter" && !t.shiftKey && (t.preventDefault(), he());
  };
  return /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    /* @__PURE__ */ s.jsx("style", { dangerouslySetInnerHTML: { __html: `
        .podcast-name-generator {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
          max-width: 920px;
          margin: 0 auto;
          padding: 20px;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          box-sizing: border-box;
        }

        .generator-container {
          width: 100%;
          box-sizing: border-box;
        }

        .header-section {
          text-align: center;
          margin-bottom: 32px;
        }

        .main-title {
          font-size: 3rem;
          font-weight: 800;
          color: #1a1a1a;
          margin: 0 0 16px 0;
          background: linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .main-subtitle {
          font-size: 1.5rem;
          color: #4a5568;
          margin: 0 0 32px 0;
          font-weight: 500;
          line-height: 1.4;
        }

        .benefits-section {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 48px;
          margin: 0 0 48px 0;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .benefit-checkmark {
          width: 24px;
          height: 24px;
          background-color: #6941C7;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .benefit-text {
          color: #2d3748;
          font-size: 1rem;
          font-weight: 500;
          white-space: nowrap;
        }

        .limit-reached-banner {
          margin-bottom: 24px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border: 1px solid #f87171;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(248, 113, 113, 0.1);
          text-align: center;
        }

        .limit-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .limit-icon {
          font-size: 2.5rem;
          margin-bottom: 8px;
        }

        .limit-text p {
          margin: 0;
          color: #991b1b;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        .initial-input-section {
          margin-bottom: 32px;
          padding: 26px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 2px solid #e2e8f0;
          border-radius: 20px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
          text-align: center;
          box-sizing: border-box;
        }

        .input-form {
          margin-bottom: 32px;
        }

        .input-container {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .button-social-container {
          display: flex;
          align-items: center;
          gap: 24px;
          justify-content: space-between;
        }

        .input-field {
          width: 100%;
          padding: 16px 20px;
          font-size: 1rem;
          border: 2px solid #e1e5e9;
          border-radius: 12px;
          resize: vertical;
          min-height: 80px;
          font-family: inherit;
          transition: all 0.2s ease;
          box-sizing: border-box;
        }

        .input-field:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-field:disabled {
          background-color: #f8f9fa;
          cursor: not-allowed;
        }

        .generate-button {
          align-self: flex-start;
          padding: 14px 28px;
          font-size: 1rem;
          font-weight: 600;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 160px;
        }

        .generate-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .generate-button.disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          cursor: not-allowed !important;
          opacity: 0.7;
        }

        .error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 12px;
          color: #dc2626;
          font-weight: 500;
          margin-bottom: 24px;
        }

        .error-icon {
          font-size: 1.2rem;
        }

        .success-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border: 1px solid #7dd3fc;
          border-radius: 12px;
          color: #0369a1;
          font-weight: 500;
          margin-bottom: 24px;
          animation: slideInFromTop 0.3s ease-out;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .success-icon {
          font-size: 1.2rem;
        }

        @keyframes slideInFromTop {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .loading-container {
          text-align: center;
          padding: 40px 20px;
          color: #666;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .results-container {
          margin-top: 32px;
          padding: 0 26px;
        }

        .favorites-section {
          margin-bottom: 32px;
          padding: 24px;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
          border: 2px solid #10b981;
          border-radius: 16px;
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
          position: relative;
          overflow: hidden;
        }

        .favorites-section::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
          animation: celebrate 3s ease-in-out infinite;
          pointer-events: none;
        }

        @keyframes celebrate {
          0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
          50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .favorites-header h3 {
          margin: 0 0 12px 0;
          color: #065f46;
          font-size: 1.5rem;
          font-weight: 800;
          text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .favorites-subtitle {
          margin: 0 0 20px 0;
          color: #047857;
          font-size: 1rem;
          line-height: 1.5;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.7);
          padding: 12px 16px;
          border-radius: 8px;
          border-left: 4px solid #10b981;
        }

        .favorites-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
        }

        .favorite-card {
          background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
          border: 2px solid #10b981;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .favorite-card::before {
          content: '✨';
          position: absolute;
          top: 12px;
          right: 12px;
          font-size: 1.2rem;
          opacity: 0.6;
        }

        .favorite-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
          border-color: #059669;
        }

        .favorite-content {
          flex: 1;
          margin-bottom: 16px;
        }

        .favorite-name {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-description {
          margin: 0 0 12px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }

        .input-section-simple {
          margin-bottom: 32px;
        }

        .input-help-message-simple {
          margin-bottom: 16px;
          text-align: center;
        }

        .input-sub-description {
          margin: 0;
          color: #075985;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        .suggestions-section {
          margin-bottom: 24px;
        }

        .suggestions-header h3 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .onboarding-banner {
          margin: 20px 0;
          padding: 16px 20px;
          background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
          border: 2px solid #0ea5e9;
          border-radius: 12px;
          animation: slideInDown 0.5s ease-out;
        }

        .onboarding-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .onboarding-icon {
          font-size: 1.5rem;
          flex-shrink: 0;
        }

        .onboarding-text {
          color: #0c4a6e;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        @keyframes slideInDown {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .results-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .result-card {
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 16px;
          padding: 20px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .result-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .result-card.pending {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-color: #cbd5e0;
        }

        .result-card.pending .result-name {
          color: #64748b;
          font-style: italic;
        }

        .result-card.pending .result-description {
          color: #64748b;
          font-style: italic;
        }

        .result-card.liked {
          border-color: #10b981;
          background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
        }

        .result-card.disliked {
          border-color: #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
          opacity: 0.8;
          transform: scale(0.98);
        }

        @keyframes flyToFavorites {
          0% {
            transform: scale(1) translateY(0) translateX(0);
            opacity: 1;
            z-index: 1000;
          }
          15% {
            transform: scale(0.95) translateY(-10px) translateX(0);
            opacity: 0.9;
          }
          50% {
            transform: scale(0.8) translateY(-100px) translateX(-20px);
            opacity: 0.7;
          }
          85% {
            transform: scale(0.6) translateY(-200px) translateX(-40px);
            opacity: 0.4;
          }
          100% {
            transform: scale(0.4) translateY(-300px) translateX(-60px);
            opacity: 0;
          }
        }

        .result-card.flying-to-favorites {
          animation: flyToFavorites 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          pointer-events: none;
          position: relative;
          z-index: 1000;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .result-card.flying-to-favorites::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #6941c7 0%, #8b5cf6 100%);
          border-radius: 12px;
          opacity: 0.2;
          animation: trailEffect 0.7s ease-out forwards;
          z-index: -1;
        }

        @keyframes trailEffect {
          0% {
            opacity: 0;
            transform: translateY(0);
          }
          20% {
            opacity: 0.3;
            transform: translateY(-20px);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px);
          }
        }

        .result-card.flying-to-favorites::before {
          content: '↗️';
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 16px;
          animation: directionArrow 0.7s ease-out forwards;
          z-index: 1001;
          pointer-events: none;
        }

        @keyframes directionArrow {
          0% {
            opacity: 0;
            transform: translateY(10px) scale(0.8);
          }
          30% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1);
          }
          70% {
            opacity: 0.6;
            transform: translateY(-50px) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px) scale(0.7);
          }
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 16px;
        }

        .result-name {
          margin: 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
          flex: 1;
        }

        .result-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }

        .feedback-buttons {
          display: flex;
          gap: 4px;
        }

        .feedback-button {
          width: 36px;
          height: 36px;
          border: 2px solid #e2e8f0;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          position: relative;
        }

        .feedback-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feedback-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .feedback-button.loading {
          animation: spin 1s linear infinite;
        }

        .like-button:hover:not(:disabled) {
          border-color: #10b981;
          background: #ecfdf5;
        }

        .like-button.active {
          border-color: #10b981;
          background: #10b981;
          color: white;
          transform: scale(1.1);
        }

        .dislike-button:hover:not(:disabled) {
          border-color: #ef4444;
          background: #fef2f2;
        }

        .dislike-button.active {
          border-color: #ef4444;
          background: #ef4444;
          color: white;
          transform: scale(1.1);
        }

        .copy-button {
          padding: 8px 12px;
          font-size: 0.85rem;
          font-weight: 500;
          color: #4b5563;
          background: #f9fafb;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .copy-button:hover:not(:disabled) {
          background: #f3f4f6;
          border-color: #9ca3af;
          transform: translateY(-1px);
        }

        .copy-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .copy-button.small {
          padding: 6px 10px;
          font-size: 0.8rem;
        }

        .result-description {
          margin: 0 0 16px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .domain-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          font-size: 0.85rem;
          margin-top: 12px;
        }

        .domain-info.inline {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 6px;
        }

        .domain-label {
          color: #64748b;
          font-weight: 500;
        }

        .domain-name {
          color: #1e293b;
          font-weight: 600;
        }

        .domain-text {
          background: #e2e8f0;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.8rem;
          color: #1e293b;
        }

        .domain-status {
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .domain-status.checking {
          color: #0369a1;
          background: #e0f2fe;
        }

        .domain-status.available {
          color: #065f46;
          background: #d1fae5;
        }

        .domain-status.taken {
          color: #991b1b;
          background: #fee2e2;
        }

        .domain-status.error {
          color: #92400e;
          background: #fef3c7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .podcast-name-generator {
            padding: 16px;
            margin: 0 16px;
          }

          .main-title {
            font-size: 2.2rem;
          }

          .main-subtitle {
            font-size: 1.2rem;
          }

          .benefits-section {
            flex-direction: column;
            gap: 16px;
            align-items: center;
          }

          .benefit-item {
            justify-content: center;
          }

          .initial-input-section {
            padding: 20px;
          }

          .button-social-container {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
          }

          .generate-button {
            align-self: stretch;
            text-align: center;
          }

          .results-container {
            padding: 0 16px;
          }

          .results-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .favorites-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .result-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
          }

          .result-actions {
            align-self: stretch;
            justify-content: space-between;
          }

          .domain-info.inline {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }

        @media (max-width: 480px) {
          .main-title {
            font-size: 1.8rem;
          }

          .main-subtitle {
            font-size: 1rem;
          }

          .benefit-text {
            font-size: 0.9rem;
          }

          .initial-input-section {
            padding: 16px;
          }

          .favorites-section {
            padding: 16px;
          }

          .result-card {
            padding: 16px;
          }
        }
      ` } }),
    /* @__PURE__ */ s.jsx("div", { className: `podcast-name-generator ${N}`, style: $, children: /* @__PURE__ */ s.jsxs("div", { className: "generator-container", children: [
      /* @__PURE__ */ s.jsxs("div", { className: "header-section", children: [
        /* @__PURE__ */ s.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
        /* @__PURE__ */ s.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
      ] }),
      /* @__PURE__ */ s.jsxs("div", { className: "benefits-section", children: [
        /* @__PURE__ */ s.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ s.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ s.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
        ] }),
        /* @__PURE__ */ s.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ s.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ s.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
        ] }),
        /* @__PURE__ */ s.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ s.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ s.jsx("span", { className: "benefit-text", children: "Instant Results" })
        ] })
      ] }),
      v && /* @__PURE__ */ s.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ s.jsxs("div", { className: "limit-content", children: [
        /* @__PURE__ */ s.jsx("span", { className: "limit-icon", children: "⚠️" }),
        /* @__PURE__ */ s.jsx("div", { className: "limit-text", children: /* @__PURE__ */ s.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
      ] }) }),
      f.length === 0 && /* @__PURE__ */ s.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ s.jsx("form", { onSubmit: we, className: "input-form", children: /* @__PURE__ */ s.jsxs("div", { className: "input-container", children: [
        /* @__PURE__ */ s.jsx(
          "textarea",
          {
            value: x,
            onChange: (t) => R(t.target.value),
            onKeyPress: ye,
            placeholder: "Describe what your podcast is about",
            className: "input-field",
            rows: 3,
            disabled: j
          }
        ),
        /* @__PURE__ */ s.jsx("div", { className: "button-social-container", children: /* @__PURE__ */ s.jsx(
          "button",
          {
            type: "submit",
            disabled: j || !x.trim() || v,
            className: `generate-button ${v ? "disabled" : ""}`,
            children: j ? "Generating..." : v ? "Daily Limit Reached" : "Generate Names"
          }
        ) })
      ] }) }) }),
      U && /* @__PURE__ */ s.jsxs("div", { className: "error-message", children: [
        /* @__PURE__ */ s.jsx("span", { className: "error-icon", children: "⚠️" }),
        U
      ] }),
      Y && /* @__PURE__ */ s.jsxs("div", { className: "success-message", children: [
        /* @__PURE__ */ s.jsx("span", { className: "success-icon", children: "✨" }),
        Y
      ] }),
      j && /* @__PURE__ */ s.jsxs("div", { className: "loading-container", children: [
        /* @__PURE__ */ s.jsx("div", { className: "loading-spinner" }),
        /* @__PURE__ */ s.jsx("p", { children: ce ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
      ] }),
      f.length > 0 && /* @__PURE__ */ s.jsxs("div", { className: "results-container", children: [
        S.length > 0 && /* @__PURE__ */ s.jsxs("div", { className: "favorites-section", children: [
          /* @__PURE__ */ s.jsxs("div", { className: "favorites-header", children: [
            /* @__PURE__ */ s.jsxs("h3", { children: [
              "🏆 Your Winning Podcast Names (",
              S.length,
              ")"
            ] }),
            /* @__PURE__ */ s.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
          ] }),
          /* @__PURE__ */ s.jsx("div", { className: "favorites-grid", children: S.map((t, n) => /* @__PURE__ */ s.jsxs("div", { className: "favorite-card", children: [
            /* @__PURE__ */ s.jsxs("div", { className: "favorite-content", children: [
              /* @__PURE__ */ s.jsx("h4", { className: "favorite-name", children: t.name }),
              /* @__PURE__ */ s.jsx("p", { className: "favorite-description", children: t.description }),
              t.suggestedDomain && /* @__PURE__ */ s.jsxs("div", { className: "domain-info inline", children: [
                /* @__PURE__ */ s.jsx("span", { className: "domain-label", children: "Domain:" }),
                /* @__PURE__ */ s.jsx("span", { className: "domain-name", children: t.suggestedDomain }),
                /* @__PURE__ */ s.jsx("span", { className: `domain-status ${t.domainStatus}`, children: t.domainStatus === "available" ? "✅ Available" : t.domainStatus === "taken" ? "❌ Taken" : t.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
              ] })
            ] }),
            /* @__PURE__ */ s.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ s.jsx(
              "button",
              {
                onClick: () => be(t.name, -1),
                className: "copy-button small",
                title: "Copy to clipboard",
                children: "📋 Copy"
              }
            ) })
          ] }, `fav-${n}`)) })
        ] }),
        /* @__PURE__ */ s.jsxs("div", { className: "input-section-simple", children: [
          /* @__PURE__ */ s.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ s.jsxs("p", { className: "input-sub-description", children: [
            "💡 Want different suggestions? Update your description below - ",
            /* @__PURE__ */ s.jsx("strong", { children: "your favorites will stay safe!" })
          ] }) }),
          /* @__PURE__ */ s.jsx("form", { onSubmit: we, className: "input-form", children: /* @__PURE__ */ s.jsxs("div", { className: "input-container", children: [
            /* @__PURE__ */ s.jsx(
              "textarea",
              {
                value: x,
                onChange: (t) => R(t.target.value),
                onKeyPress: ye,
                placeholder: "Describe what your podcast is about",
                className: "input-field",
                rows: 3,
                disabled: j
              }
            ),
            /* @__PURE__ */ s.jsx("div", { className: "button-social-container", children: /* @__PURE__ */ s.jsx(
              "button",
              {
                type: "submit",
                disabled: j || !x.trim() || v,
                className: `generate-button ${v ? "disabled" : ""}`,
                children: j ? "Generating..." : v ? "Daily Limit Reached" : "Generate Names"
              }
            ) })
          ] }) })
        ] }),
        /* @__PURE__ */ s.jsxs("div", { className: "suggestions-section", children: [
          /* @__PURE__ */ s.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ s.jsx("h3", { children: "🎯 Current Suggestions" }) }),
          /* @__PURE__ */ s.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ s.jsxs("div", { className: "onboarding-content", children: [
            /* @__PURE__ */ s.jsx("span", { className: "onboarding-icon", children: "💡" }),
            /* @__PURE__ */ s.jsxs("div", { className: "onboarding-text", children: [
              /* @__PURE__ */ s.jsx("strong", { children: "Smart AI Learning:" }),
              " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
            ] })
          ] }) }),
          /* @__PURE__ */ s.jsx("div", { className: "results-grid", children: f.map((t, n) => {
            const r = ie.find((c) => c.index === n), i = (r == null ? void 0 : r.liked) === !0, l = (r == null ? void 0 : r.liked) === !1, o = me.has(n), a = H.has(n);
            return /* @__PURE__ */ s.jsxs(
              "div",
              {
                className: `result-card ${i ? "liked" : ""} ${l ? "disliked" : ""} ${o ? "pending" : ""} ${ae.has(n) ? "flying-to-favorites" : ""}`,
                style: {
                  opacity: o ? 0.6 : 1,
                  pointerEvents: o ? "none" : "auto"
                },
                children: [
                  /* @__PURE__ */ s.jsxs("div", { className: "result-header", children: [
                    /* @__PURE__ */ s.jsx("h4", { className: "result-name", children: o ? i ? "Generating new suggestion..." : "Generating better suggestion..." : t.name }),
                    /* @__PURE__ */ s.jsxs("div", { className: "result-actions", children: [
                      /* @__PURE__ */ s.jsxs("div", { className: "feedback-buttons", children: [
                        /* @__PURE__ */ s.jsx(
                          "button",
                          {
                            onClick: () => xe(n, !0),
                            className: `feedback-button like-button ${i ? "active" : ""}`,
                            title: "I like this name",
                            disabled: o,
                            children: "👍"
                          }
                        ),
                        /* @__PURE__ */ s.jsx(
                          "button",
                          {
                            onClick: () => xe(n, !1),
                            className: `feedback-button dislike-button ${l ? "active" : ""} ${o ? "loading" : ""}`,
                            title: o ? "Generating replacement..." : "I don't like this name",
                            disabled: o,
                            children: o ? "🔄" : "👎"
                          }
                        )
                      ] }),
                      /* @__PURE__ */ s.jsx(
                        "button",
                        {
                          onClick: () => be(t.name, n),
                          className: "copy-button",
                          title: "Copy podcast name",
                          disabled: o,
                          children: O === n ? "✓ Copied!" : "📋 Copy"
                        }
                      )
                    ] })
                  ] }),
                  /* @__PURE__ */ s.jsx("p", { className: "result-description", children: o ? i ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : t.description }),
                  t.suggestedDomain && !a && /* @__PURE__ */ s.jsxs("div", { className: "domain-info inline", children: [
                    /* @__PURE__ */ s.jsx("span", { className: "domain-label", children: "Domain:" }),
                    /* @__PURE__ */ s.jsxs("code", { className: "domain-text", children: [
                      t.suggestedDomain,
                      ".com"
                    ] }),
                    /* @__PURE__ */ s.jsxs("span", { className: `domain-status ${t.domainStatus}`, children: [
                      (t.domainStatus === "checking" || H.has(n)) && "⏳ Checking...",
                      t.domainStatus === "available" && "✅ Available",
                      t.domainStatus === "taken" && "❌ Taken",
                      t.domainStatus === "error" && "⚠️ Check manually"
                    ] })
                  ] })
                ]
              },
              n
            );
          }) })
        ] })
      ] })
    ] }) })
  ] });
}
export {
  Pe as default
};
